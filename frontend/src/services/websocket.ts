/**
 * WebSocket Service for Real-time Communication
 * Handles real-time notifications, updates, and live data
 */

import { store } from '../store'
import { addNotification } from '../store/slices/notificationSlice'

export interface WebSocketMessage {
  type: string
  payload: any
  timestamp: number
  id?: string
}

export interface NotificationData {
  id: string
  type: 'info' | 'warning' | 'success' | 'error' | 'urgent'
  category: 'hr' | 'finance' | 'project' | 'system' | 'communication'
  title: string
  titleAr: string
  message: string
  messageAr: string
  timestamp: string
  isRead: boolean
  isStarred: boolean
  actionRequired: boolean
  priority: 'low' | 'medium' | 'high' | 'urgent'
  relatedUser?: string
  relatedEntity?: string
  actions?: Array<{
    id: string
    label: string
    labelAr: string
    action: string
    variant: 'primary' | 'secondary' | 'danger'
  }>
  metadata?: Record<string, any>
}

export interface NotificationAction {
  label: string
  action: string
  variant?: 'primary' | 'secondary' | 'danger'
}

class WebSocketService {
  private ws: WebSocket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 3
  private reconnectInterval = 5000
  private heartbeatInterval: NodeJS.Timeout | null = null
  private reconnectTimeout: NodeJS.Timeout | null = null // FIXED: Track reconnect timeout
  private messageQueue: WebSocketMessage[] = []
  private isConnected = false
  private listeners: Map<string, ((data: any) => void)[]> = new Map()
  private connectionEnabled = true
  private lastConnectionAttempt = 0

  constructor() {
    // Only try to connect if WebSocket is enabled and backend is likely available
    if (this.shouldAttemptConnection()) {
      this.connect()
    } else {
      console.log('WebSocket connection disabled - backend not available')
    }
  }

  /**
   * Check if we should attempt WebSocket connection
   */
  private shouldAttemptConnection(): boolean {
    // Don't attempt connection too frequently
    const now = Date.now()
    if (now - this.lastConnectionAttempt < 10000) { // 10 seconds
      return false
    }

    // Check if WebSocket is explicitly disabled
    const wsDisabled = import.meta.env.VITE_DISABLE_WEBSOCKET === 'true'
    if (wsDisabled) {
      return false
    }

    // Check if we have a token
    const token = localStorage.getItem('access_token')
    if (!token) {
      return false
    }

    return this.connectionEnabled
  }

  /**
   * Connect to WebSocket server
   */
  connect(): void {
    if (!this.shouldAttemptConnection()) {
      return
    }

    try {
      this.lastConnectionAttempt = Date.now()
      const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:8000/ws'
      const token = localStorage.getItem('access_token') || localStorage.getItem('token')

      if (!token) {
        console.warn('No access token found, skipping WebSocket connection')
        return
      }

      console.log('Attempting WebSocket connection to:', wsUrl)
      this.ws = new WebSocket(`${wsUrl}?token=${token}`)

      // Set connection timeout
      const connectionTimeout = setTimeout(() => {
        if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
          console.warn('WebSocket connection timeout')
          this.ws.close()
          this.handleConnectionFailure()
        }
      }, 5000) // 5 second timeout

      this.ws.onopen = () => {
        clearTimeout(connectionTimeout)
        this.handleOpen()
      }

      this.ws.onmessage = this.handleMessage.bind(this)
      this.ws.onclose = (event) => {
        clearTimeout(connectionTimeout)
        this.handleClose(event)
      }

      this.ws.onerror = (error) => {
        clearTimeout(connectionTimeout)
        this.handleError(error)
      }

    } catch (error) {
      console.error('WebSocket connection error:', error)
      this.handleConnectionFailure()
    }
  }

  /**
   * Handle connection failure
   */
  private handleConnectionFailure(): void {
    console.warn('WebSocket connection failed, disabling further attempts for this session')
    this.connectionEnabled = false
    this.isConnected = false

    // Emit connection failure event
    this.emit('connection_failed', {
      attempts: this.reconnectAttempts,
      timestamp: Date.now()
    })
  }

  /**
   * Handle WebSocket open event
   */
  private handleOpen(): void {
    console.log('WebSocket connected')
    this.isConnected = true
    this.reconnectAttempts = 0

    // Start heartbeat
    this.startHeartbeat()

    // Send queued messages
    this.sendQueuedMessages()

    // Notify listeners
    this.emit('connected', { timestamp: Date.now() })
  }

  /**
   * Handle incoming WebSocket messages
   */
  private handleMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data)

      switch (message.type) {
        case 'notification':
          this.handleNotification(message.payload)
          break
        case 'user_update':
          this.handleUserUpdate(message.payload)
          break
        case 'system_update':
          this.handleSystemUpdate(message.payload)
          break
        case 'heartbeat':
          // Heartbeat response - connection is alive
          break
        default:
          // Emit to custom listeners
          this.emit(message.type, message.payload)
      }
    } catch (error) {
      console.error('Error parsing WebSocket message:', error)
    }
  }

  /**
   * Handle WebSocket close event
   */
  private handleClose(event: CloseEvent): void {
    console.log('WebSocket disconnected:', event.code, event.reason)
    this.isConnected = false
    this.stopHeartbeat()

    // Attempt to reconnect if not a normal closure
    if (event.code !== 1000) {
      this.scheduleReconnect()
    }

    this.emit('disconnected', { code: event.code, reason: event.reason })
  }

  /**
   * Handle WebSocket error event
   */
  private handleError(error: Event): void {
    console.warn('WebSocket error (this is normal if backend is not running):', error)
    this.emit('error', error)

    // Don't spam error logs - disable connection attempts
    this.handleConnectionFailure()
  }

  /**
   * Handle incoming notifications
   */
  private handleNotification(notification: NotificationData): void {
    // Add to Redux store
    store.dispatch(addNotification(notification))

    // Show browser notification if permission granted
    this.showBrowserNotification(notification)

    // Emit to listeners
    this.emit('notification', notification)
  }

  /**
   * Handle user updates
   */
  private handleUserUpdate(data: any): void {
    this.emit('user_update', data)
  }

  /**
   * Handle system updates
   */
  private handleSystemUpdate(data: any): void {
    this.emit('system_update', data)
  }

  /**
   * Show browser notification
   */
  private showBrowserNotification(notification: NotificationData): void {
    if ('Notification' in window && Notification.permission === 'granted') {
      const browserNotification = new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
        tag: notification.id,
        requireInteraction: notification.type === 'error'
      })

      browserNotification.onclick = () => {
        window.focus()
        browserNotification.close()

        // Mark as read - would need to implement this action
        // store.dispatch(markAsRead(notification.id))
      }

      // Auto close after 5 seconds for non-error notifications
      if (notification.type !== 'error') {
        setTimeout(() => {
          browserNotification.close()
        }, 5000)
      }
    }
  }

  /**
   * Send message to WebSocket server
   */
  send(message: WebSocketMessage): void {
    if (this.isConnected && this.ws) {
      this.ws.send(JSON.stringify(message))
    } else if (this.connectionEnabled) {
      // Queue message for later if connection is enabled
      this.messageQueue.push(message)

      // Limit queue size to prevent memory issues
      if (this.messageQueue.length > 100) {
        this.messageQueue.shift() // Remove oldest message
      }
    } else {
      // Connection disabled - don't queue messages
      console.debug('WebSocket: Message not sent - connection disabled')
    }
  }

  /**
   * Send queued messages
   */
  private sendQueuedMessages(): void {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()
      if (message && this.ws) {
        this.ws.send(JSON.stringify(message))
      }
    }
  }

  /**
   * Start heartbeat to keep connection alive
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected) {
        this.send({
          type: 'heartbeat',
          payload: {},
          timestamp: Date.now()
        })
      }
    }, 30000) // Send heartbeat every 30 seconds
  }

  /**
   * Stop heartbeat
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }

  /**
   * Schedule reconnection attempt
   */
  private scheduleReconnect(): void {
    if (!this.connectionEnabled) {
      return
    }

    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1)

      console.log(`WebSocket: Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`)

      // FIXED: Store timeout reference for cleanup
      this.reconnectTimeout = setTimeout(() => {
        if (this.connectionEnabled) {
          this.connect()
        }
      }, delay)
    } else {
      console.warn('WebSocket: Max reconnection attempts reached, disabling further attempts')
      this.handleConnectionFailure()
    }
  }

  /**
   * Add event listener
   */
  on(event: string, callback: (data: any) => void): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event)!.push(callback)
  }

  /**
   * Remove event listener
   */
  off(event: string, callback: (data: any) => void): void {
    const eventListeners = this.listeners.get(event)
    if (eventListeners) {
      const index = eventListeners.indexOf(callback)
      if (index > -1) {
        eventListeners.splice(index, 1)
      }
    }
  }

  /**
   * Emit event to listeners
   */
  private emit(event: string, data: any): void {
    const eventListeners = this.listeners.get(event)
    if (eventListeners) {
      eventListeners.forEach(callback => callback(data))
    }
  }

  /**
   * Request browser notification permission
   */
  static async requestNotificationPermission(): Promise<NotificationPermission> {
    if ('Notification' in window) {
      return await Notification.requestPermission()
    }
    return 'denied'
  }

  /**
   * Check if connected
   */
  isWebSocketConnected(): boolean {
    return this.isConnected
  }

  /**
   * Disconnect WebSocket
   */
  disconnect(): void {
    if (this.ws) {
      this.ws.close(1000, 'Manual disconnect')
      this.ws = null
    }
    this.stopHeartbeat()
    this.isConnected = false
  }

  /**
   * Get connection status
   */
  getConnectionStatus(): {
    connected: boolean
    enabled: boolean
    reconnectAttempts: number
    queuedMessages: number
    lastAttempt: number
  } {
    return {
      connected: this.isConnected,
      enabled: this.connectionEnabled,
      reconnectAttempts: this.reconnectAttempts,
      queuedMessages: this.messageQueue.length,
      lastAttempt: this.lastConnectionAttempt
    }
  }

  /**
   * Enable WebSocket connection
   */
  enableConnection(): void {
    this.connectionEnabled = true
    this.reconnectAttempts = 0

    if (!this.isConnected) {
      console.log('WebSocket: Connection enabled, attempting to connect...')
      this.connect()
    }
  }

  /**
   * Disable WebSocket connection
   */
  disableConnection(): void {
    this.connectionEnabled = false

    if (this.ws) {
      this.ws.close(1000, 'Connection disabled')
    }

    // Clear message queue
    this.messageQueue = []

    console.log('WebSocket: Connection disabled')
  }

  /**
   * FIXED: Comprehensive cleanup method to prevent memory leaks
   */
  destroy(): void {
    console.log('WebSocket: Destroying service and cleaning up resources')

    // Disable connection
    this.connectionEnabled = false

    // Clear all timeouts and intervals
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }

    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout)
      this.reconnectTimeout = null
    }

    // Close WebSocket connection
    if (this.ws) {
      this.ws.close(1000, 'Service destroyed')
      this.ws = null
    }

    // Clear all listeners
    this.listeners.clear()

    // Clear message queue
    this.messageQueue = []

    // Reset state
    this.isConnected = false
    this.reconnectAttempts = 0
  }
}

// Create singleton instance
export const webSocketService = new WebSocketService()

// Export for use in components
export default webSocketService
