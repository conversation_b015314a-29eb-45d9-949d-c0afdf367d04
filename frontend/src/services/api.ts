/**
 * API Service Layer for EMS Application
 * Handles all HTTP requests to the Django backend
 */

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api'

// API Response Types
export interface ApiResponse<T = unknown> {
  data: T
  message?: string
  status: number
}

export interface ApiErrorInterface {
  message: string
  status: number
  details?: Record<string, unknown>
}

// Authentication Types
export interface LoginCredentials {
  username: string
  password: string
}

export interface AuthResponse {
  access: string
  refresh: string
  user: User
}

export interface User {
  id: number
  username: string
  email: string
  first_name: string
  last_name: string
  role: UserRole
  profile: UserProfile
}

export interface UserRole {
  id: string
  name: string
  nameAr: string
  permissions: Permission[]
  dashboardConfig: DashboardConfig
}

export interface Permission {
  module: string
  actions: string[]
}

export interface DashboardConfig {
  allowedRoutes: string[]
  defaultWidgets: string[]
  customizations: Record<string, unknown>
}

export interface UserProfile {
  avatar?: string
  phone?: string
  department?: string
  position?: string
  preferred_language: 'ar' | 'en'
  timezone: string
}

// HTTP Client Class
class ApiClient {
  private baseURL: string
  private token: string | null = null

  constructor(baseURL: string) {
    this.baseURL = baseURL
    this.token = localStorage.getItem('access_token') || localStorage.getItem('token')
    console.log('ApiClient initialized with token:', !!this.token)

    // Ensure token is available for immediate use
    if (this.token) {
      console.log('Token found in localStorage, API client ready')
    }
  }

  // Set authentication token
  setToken(token: string | null) {
    this.token = token
    if (token) {
      localStorage.setItem('access_token', token)
      localStorage.setItem('token', token) // Backup storage
      console.log('Token stored in localStorage')
    } else {
      localStorage.removeItem('access_token')
      localStorage.removeItem('token')
      console.log('Token removed from localStorage')
    }
  }

  // Get authentication headers
  private getHeaders(): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    }

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`
    }

    return headers
  }

  // FIXED: Generic request method with improved error handling and retry logic
  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
    retryCount = 0
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`
    const maxRetries = 3
    const retryDelay = Math.pow(2, retryCount) * 1000 // Exponential backoff

    const config: RequestInit = {
      ...options,
      headers: {
        ...this.getHeaders(),
        ...options.headers,
      },
      // FIXED: Add timeout to prevent hanging requests
      signal: AbortSignal.timeout(10000), // 10 second timeout
    }

    try {
      const response = await fetch(url, config)

      // FIXED: Handle 401 unauthorized with token refresh
      if (response.status === 401 && retryCount === 0) {
        try {
          await this.refreshToken()
          return this.request(endpoint, options, retryCount + 1)
        } catch (refreshError) {
          // If refresh fails, clear tokens and throw original error
          this.setToken(null)
          localStorage.removeItem('refresh_token')
        }
      }

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))

        // FIXED: Retry on server errors (5xx) but not client errors (4xx)
        if (response.status >= 500 && retryCount < maxRetries) {
          console.warn(`API request failed with ${response.status}, retrying in ${retryDelay}ms...`)
          await new Promise(resolve => setTimeout(resolve, retryDelay))
          return this.request(endpoint, options, retryCount + 1)
        }

        throw new ApiError({
          message: errorData.message || `HTTP ${response.status}: ${response.statusText}`,
          status: response.status,
          details: errorData,
        })
      }

      const data = await response.json()

      return {
        data,
        status: response.status,
        message: data.message,
      }
    } catch (error) {
      // FIXED: Handle network errors with retry
      if (error instanceof ApiError) {
        throw error
      }

      // Retry on network errors
      if (retryCount < maxRetries && (error as Error).name !== 'AbortError') {
        console.warn(`Network error, retrying in ${retryDelay}ms...`, error)
        await new Promise(resolve => setTimeout(resolve, retryDelay))
        return this.request(endpoint, options, retryCount + 1)
      }

      throw new ApiError({
        message: error instanceof Error ? error.message : 'Network error occurred',
        status: 0,
        details: error,
      })
    }
  }

  // FIXED: Add token refresh method
  private async refreshToken(): Promise<void> {
    const refreshToken = localStorage.getItem('refresh_token')
    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    const response = await fetch(`${this.baseURL}/auth/refresh/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ refresh: refreshToken }),
    })

    if (!response.ok) {
      throw new Error('Token refresh failed')
    }

    const data = await response.json()
    this.setToken(data.access)
  }

  // HTTP Methods
  async get<T>(endpoint: string, config?: { params?: Record<string, string | number | boolean | undefined> }): Promise<ApiResponse<T>> {
    // Handle query parameters from config
    let url = endpoint
    if (config?.params) {
      const searchParams = new URLSearchParams()
      Object.entries(config.params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value))
        }
      })
      url += `?${searchParams.toString()}`
    }
    return this.request<T>(url, { method: 'GET' })
  }

  async post<T>(endpoint: string, data?: any, config?: Record<string, unknown>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async patch<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' })
  }

  // File upload method
  async uploadFile<T>(endpoint: string, file: File, additionalData?: Record<string, string | number | boolean>): Promise<ApiResponse<T>> {
    const formData = new FormData()
    formData.append('file', file)

    if (additionalData) {
      Object.keys(additionalData).forEach(key => {
        formData.append(key, String(additionalData[key]))
      })
    }

    const headers: HeadersInit = {}
    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`
    }

    return this.request<T>(endpoint, {
      method: 'POST',
      body: formData,
      headers,
    })
  }
}

// Create API client instance
export const apiClient = new ApiClient(API_BASE_URL)

// Custom error class
class ApiError extends Error {
  status: number
  details?: Record<string, unknown>

  constructor({ message, status, details }: { message: string; status: number; details?: Record<string, unknown> }) {
    super(message)
    this.name = 'ApiError'
    this.status = status
    this.details = details
  }
}

export { ApiError }

// Authentication API
export const authAPI = {
  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    try {
      const response = await apiClient.post<AuthResponse>('/auth/login/', credentials)

      // Store tokens
      apiClient.setToken(response.data.access)
      localStorage.setItem('refresh_token', response.data.refresh)

      return response.data
    } catch (error) {
      throw error
    }
  },

  logout: async (): Promise<void> => {
    try {
      const refreshToken = localStorage.getItem('refresh_token')
      if (refreshToken) {
        await apiClient.post('/auth/logout/', { refresh: refreshToken })
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      apiClient.setToken(null)
      localStorage.removeItem('refresh_token')
    }
  },

  getCurrentUser: async (): Promise<User> => {
    const response = await apiClient.get<User>('/auth/user/')
    return response.data
  },

  updateProfile: async (profileData: Partial<User>): Promise<User> => {
    const response = await apiClient.patch<User>('/auth/profile/', profileData)
    return response.data
  },

  changePassword: async (passwordData: {
    current_password: string
    new_password: string
  }): Promise<{ message: string }> => {
    const response = await apiClient.post<{ message: string }>('/auth/change-password/', passwordData)
    return response.data
  },
}

// Dashboard API
export interface DashboardStats {
  total_employees: number
  total_departments: number
  active_projects: number
  pending_tasks: number
  pending_leave_requests: number
  monthly_expenses: number
  system_health: {
    cpu_usage: number
    memory_usage: number
    disk_usage: number
  }
}

export const dashboardAPI = {
  getStats: async (): Promise<DashboardStats> => {
    // Import deduplicateRequest dynamically to avoid circular imports
    const { deduplicateRequest } = await import('../utils/apiCache')

    return deduplicateRequest(
      'dashboard-stats',
      async () => {
        console.log('📊 Fetching dashboard stats from API')
        const response = await apiClient.get<DashboardStats>('/dashboard-stats/')
        return response.data
      },
      10000 // 10 second deduplication window for dashboard stats
    )
  },
}

// Utility functions
export const isApiError = (error: unknown): error is ApiError => {
  return error instanceof ApiError
}

export const handleApiError = (error: unknown): string => {
  if (isApiError(error)) {
    return error.message
  }

  if (error instanceof Error) {
    return error.message
  }

  return 'An unexpected error occurred'
}
