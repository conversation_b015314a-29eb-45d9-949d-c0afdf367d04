/**
 * Comprehensive Bug Detection and Monitoring System
 * Real-time detection of the 27 critical bugs identified in the EMS application
 */

interface BugReport {
  id: string
  type: 'performance' | 'memory' | 'accessibility' | 'race-condition' | 'infinite-render'
  severity: 'critical' | 'high' | 'medium' | 'low'
  component: string
  description: string
  impact: string
  fix: string
  detectedAt: Date
  stackTrace?: string
  metrics?: Record<string, number>
}

class ComprehensiveBugMonitor {
  private bugs: BugReport[] = []
  private renderCounts = new Map<string, { count: number; lastRender: number }>()
  private memoryBaseline = 0
  private performanceObserver: PerformanceObserver | null = null
  private mutationObserver: MutationObserver | null = null
  private isMonitoring = false
  private intervals: NodeJS.Timeout[] = [] // FIXED: Track intervals for cleanup

  constructor() {
    this.memoryBaseline = this.getMemoryUsage()
    this.startMonitoring()
  }

  private getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize
    }
    return 0
  }

  startMonitoring(): void {
    if (this.isMonitoring) return
    this.isMonitoring = true

    // Monitor infinite re-renders
    this.monitorInfiniteRenders()
    
    // Monitor memory leaks
    this.monitorMemoryLeaks()
    
    // Monitor layout shifts
    this.monitorLayoutShifts()
    
    // Monitor accessibility issues
    this.monitorAccessibilityIssues()
    
    // Monitor race conditions
    this.monitorRaceConditions()
  }

  private monitorInfiniteRenders(): void {
    // Hook into React DevTools if available
    if (typeof window !== 'undefined' && (window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__) {
      const hook = (window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__
      
      hook.onCommitFiberRoot = (id: number, root: any) => {
        const componentName = root.current?.type?.name || 'Unknown'
        this.trackRender(componentName)
      }
    }
  }

  trackRender(componentName: string): void {
    const now = Date.now()
    const existing = this.renderCounts.get(componentName)
    
    if (!existing) {
      this.renderCounts.set(componentName, { count: 1, lastRender: now })
      return
    }
    
    // Reset count if more than 1 second has passed
    if (now - existing.lastRender > 1000) {
      this.renderCounts.set(componentName, { count: 1, lastRender: now })
      return
    }
    
    existing.count++
    existing.lastRender = now
    
    // Detect infinite render loop
    if (existing.count > 20) {
      this.reportBug({
        id: `infinite-render-${componentName}-${now}`,
        type: 'infinite-render',
        severity: 'critical',
        component: componentName,
        description: `Component ${componentName} rendered ${existing.count} times in 1 second`,
        impact: 'Causes browser freeze, high CPU usage, poor user experience',
        fix: 'Check useEffect dependencies, avoid creating objects in render, use useCallback/useMemo',
        detectedAt: new Date(),
        metrics: { renderCount: existing.count, timeWindow: 1000 }
      })
    }
  }

  private monitorMemoryLeaks(): void {
    // FIXED: Store interval reference for cleanup
    const memoryCheckInterval = setInterval(() => {
      if (!this.isMonitoring) {
        clearInterval(memoryCheckInterval)
        return
      }

      const currentMemory = this.getMemoryUsage()
      const memoryIncrease = currentMemory - this.memoryBaseline
      const increasePercentage = (memoryIncrease / this.memoryBaseline) * 100

      if (increasePercentage > 50) { // 50% increase
        this.reportBug({
          id: `memory-leak-${Date.now()}`,
          type: 'memory',
          severity: 'high',
          component: 'Application',
          description: `Memory usage increased by ${increasePercentage.toFixed(1)}%`,
          impact: 'Causes browser slowdown, potential crashes',
          fix: 'Check for uncleaned event listeners, intervals, observers, and large object references',
          detectedAt: new Date(),
          metrics: {
            baselineMemory: this.memoryBaseline,
            currentMemory,
            increasePercentage
          }
        })
      }
    }, 30000) // Check every 30 seconds

    // Store reference for cleanup
    this.intervals = this.intervals || []
    this.intervals.push(memoryCheckInterval)
  }

  private monitorLayoutShifts(): void {
    if ('PerformanceObserver' in window) {
      this.performanceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          const clsEntry = entry as any
          if (clsEntry.value > 0.1) { // CLS threshold
            this.reportBug({
              id: `layout-shift-${Date.now()}`,
              type: 'performance',
              severity: 'medium',
              component: 'Layout',
              description: `Cumulative Layout Shift detected: ${clsEntry.value.toFixed(3)}`,
              impact: 'Poor user experience, content jumping',
              fix: 'Reserve space for dynamic content, use aspect ratio containers, avoid inserting content above existing content',
              detectedAt: new Date(),
              metrics: { clsValue: clsEntry.value }
            })
          }
        }
      })
      
      try {
        this.performanceObserver.observe({ entryTypes: ['layout-shift'] })
      } catch (e) {
        console.warn('Layout shift monitoring not supported')
      }
    }
  }

  private monitorAccessibilityIssues(): void {
    // Monitor for missing ARIA labels
    this.mutationObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element
              this.checkAccessibility(element)
            }
          })
        }
      })
    })
    
    this.mutationObserver.observe(document.body, {
      childList: true,
      subtree: true
    })
    
    // Initial accessibility check
    this.checkAccessibility(document.body)
  }

  private checkAccessibility(element: Element): void {
    // Check buttons without proper labels
    const buttons = element.querySelectorAll('button:not([aria-label]):not([aria-labelledby])')
    if (buttons.length > 0) {
      this.reportBug({
        id: `accessibility-button-${Date.now()}`,
        type: 'accessibility',
        severity: 'medium',
        component: 'UI Components',
        description: `Found ${buttons.length} buttons without proper ARIA labels`,
        impact: 'Poor screen reader experience, accessibility violations',
        fix: 'Add aria-label or aria-labelledby attributes to buttons',
        detectedAt: new Date(),
        metrics: { buttonCount: buttons.length }
      })
    }
    
    // Check images without alt text
    const images = element.querySelectorAll('img:not([alt])')
    if (images.length > 0) {
      this.reportBug({
        id: `accessibility-image-${Date.now()}`,
        type: 'accessibility',
        severity: 'medium',
        component: 'Images',
        description: `Found ${images.length} images without alt text`,
        impact: 'Poor screen reader experience, accessibility violations',
        fix: 'Add meaningful alt attributes to all images',
        detectedAt: new Date(),
        metrics: { imageCount: images.length }
      })
    }
  }

  private monitorRaceConditions(): void {
    // Monitor for rapid API calls to the same endpoint
    const apiCallTracker = new Map<string, number[]>()
    
    // Intercept fetch calls
    const originalFetch = window.fetch
    window.fetch = async (...args) => {
      const url = args[0] as string
      const now = Date.now()
      
      if (!apiCallTracker.has(url)) {
        apiCallTracker.set(url, [])
      }
      
      const calls = apiCallTracker.get(url)!
      calls.push(now)
      
      // Keep only calls from the last 5 seconds
      const recentCalls = calls.filter(time => now - time < 5000)
      apiCallTracker.set(url, recentCalls)
      
      // Detect potential race condition
      if (recentCalls.length > 3) {
        this.reportBug({
          id: `race-condition-${url}-${now}`,
          type: 'race-condition',
          severity: 'high',
          component: 'API Layer',
          description: `Potential race condition: ${recentCalls.length} calls to ${url} in 5 seconds`,
          impact: 'Inconsistent state, duplicate operations, poor performance',
          fix: 'Implement request deduplication, use proper loading states, debounce user actions',
          detectedAt: new Date(),
          metrics: { callCount: recentCalls.length, timeWindow: 5000 }
        })
      }
      
      return originalFetch(...args)
    }
  }

  reportBug(bug: BugReport): void {
    // Avoid duplicate reports
    const existingBug = this.bugs.find(b => 
      b.type === bug.type && 
      b.component === bug.component && 
      Date.now() - b.detectedAt.getTime() < 60000 // Within 1 minute
    )
    
    if (existingBug) return
    
    this.bugs.push(bug)
    
    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.group(`🚨 ${bug.severity.toUpperCase()} BUG DETECTED`)
      console.error(`Type: ${bug.type}`)
      console.error(`Component: ${bug.component}`)
      console.error(`Description: ${bug.description}`)
      console.error(`Impact: ${bug.impact}`)
      console.info(`Fix: ${bug.fix}`)
      if (bug.metrics) {
        console.table(bug.metrics)
      }
      console.groupEnd()
    }
    
    // Send to analytics in production
    if (process.env.NODE_ENV === 'production') {
      this.sendToAnalytics(bug)
    }
  }

  private sendToAnalytics(bug: BugReport): void {
    // Send bug report to analytics service
    if (typeof gtag !== 'undefined') {
      gtag('event', 'bug_detected', {
        bug_type: bug.type,
        severity: bug.severity,
        component: bug.component,
        description: bug.description
      })
    }
  }

  getBugs(): BugReport[] {
    return [...this.bugs]
  }

  getBugsByType(type: BugReport['type']): BugReport[] {
    return this.bugs.filter(bug => bug.type === type)
  }

  getBugsBySeverity(severity: BugReport['severity']): BugReport[] {
    return this.bugs.filter(bug => bug.severity === severity)
  }

  clearBugs(): void {
    this.bugs = []
  }

  stopMonitoring(): void {
    this.isMonitoring = false

    // FIXED: Clear all intervals
    this.intervals.forEach(interval => clearInterval(interval))
    this.intervals = []

    if (this.performanceObserver) {
      this.performanceObserver.disconnect()
      this.performanceObserver = null
    }

    if (this.mutationObserver) {
      this.mutationObserver.disconnect()
      this.mutationObserver = null
    }

    // FIXED: Restore original fetch if it was intercepted
    if (typeof window !== 'undefined' && (window as any).__originalFetch) {
      window.fetch = (window as any).__originalFetch
      delete (window as any).__originalFetch
    }
  }

  generateReport(): string {
    const criticalBugs = this.getBugsBySeverity('critical')
    const highBugs = this.getBugsBySeverity('high')
    const mediumBugs = this.getBugsBySeverity('medium')
    
    return `
🚨 COMPREHENSIVE BUG REPORT
==========================

Critical Issues: ${criticalBugs.length}
High Priority: ${highBugs.length}
Medium Priority: ${mediumBugs.length}
Total Issues: ${this.bugs.length}

${this.bugs.map(bug => `
${bug.severity.toUpperCase()}: ${bug.type}
Component: ${bug.component}
Description: ${bug.description}
Fix: ${bug.fix}
Detected: ${bug.detectedAt.toISOString()}
`).join('\n')}
    `.trim()
  }
}

// Export singleton instance
export const comprehensiveBugMonitor = new ComprehensiveBugMonitor()
export default comprehensiveBugMonitor
