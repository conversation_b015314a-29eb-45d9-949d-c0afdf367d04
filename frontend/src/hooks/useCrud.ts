/**
 * Generic CRUD Hook with Data Invalidation
 * Provides standardized state management and operations for CRUD functionality
 * FIXED: Added data invalidation and synchronization
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import CrudService, { CrudOptions, CrudFilters, CrudResponse } from '@/services/crudService'
import { useDataInvalidation, DataInvalidationEvent } from '../utils/dataInvalidation'

export interface UseCrudOptions<T> {
  service: CrudService
  initialFilters?: CrudFilters
  autoLoad?: boolean
  pageSize?: number
  entityType?: string // FIXED: Added entity type for data invalidation
  enableInvalidation?: boolean // FIXED: Option to enable/disable invalidation
}

export interface UseCrudReturn<T> {
  // Data state
  items: T[]
  selectedItem: T | null
  total: number
  page: number
  pageSize: number

  // Loading states
  loading: boolean
  creating: boolean
  updating: boolean
  deleting: boolean

  // Error state
  error: string | null

  // Filters and search
  filters: CrudFilters
  searchQuery: string
  sortBy: string
  sortOrder: 'asc' | 'desc'

  // CRUD operations
  loadItems: (options?: CrudOptions) => Promise<void>
  createItem: (data: Partial<T>) => Promise<T>
  updateItem: (id: string | number, data: Partial<T>) => Promise<T>
  deleteItem: (id: string | number) => Promise<void>
  getItem: (id: string | number) => Promise<T>

  // Bulk operations
  bulkCreate: (items: Partial<T>[]) => Promise<T[]>
  bulkUpdate: (updates: { id: string | number; data: Partial<T> }[]) => Promise<T[]>
  bulkDelete: (ids: (string | number)[]) => Promise<void>

  // Search and filter
  setFilters: (filters: CrudFilters) => void
  setSearchQuery: (query: string) => void
  setSorting: (sortBy: string, sortOrder: 'asc' | 'desc') => void
  clearFilters: () => void

  // Selection
  selectItem: (item: T | null) => void

  // Pagination
  setPage: (page: number) => void
  setPageSize: (pageSize: number) => void

  // Export/Import
  exportData: (format?: 'csv' | 'excel' | 'pdf') => Promise<Blob>
  importData: (file: File) => Promise<{ success: number; errors: string[] }>

  // Refresh
  refresh: () => Promise<void>

  // Clear error
  clearError: () => void
}

export function useCrud<T = Record<string, unknown> & { id: string | number }>({
  service,
  initialFilters = {},
  autoLoad = true,
  pageSize: initialPageSize = 20,
  entityType = 'item', // FIXED: Default entity type
  enableInvalidation = true // FIXED: Enable invalidation by default
}: UseCrudOptions<T>): UseCrudReturn<T> {
  // Data state
  const [items, setItems] = useState<T[]>([])
  const [selectedItem, setSelectedItem] = useState<T | null>(null)
  const [total, setTotal] = useState(0)
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(initialPageSize)

  // Loading states
  const [loading, setLoading] = useState(false)
  const [creating, setCreating] = useState(false)
  const [updating, setUpdating] = useState(false)
  const [deleting, setDeleting] = useState(false)

  // Error state
  const [error, setError] = useState<string | null>(null)

  // Filters and search
  const [filters, setFilters] = useState<CrudFilters>(initialFilters)
  const [searchQuery, setSearchQuery] = useState('')
  const [sortBy, setSortBy] = useState('')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')

  // FIXED: Data invalidation system
  const { subscribe, emit } = useDataInvalidation()

  // Track current request to prevent duplicates
  const currentRequestRef = useRef<string | null>(null)
  const requestTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Clear error
  const clearError = useCallback(() => {
    setError(null)
  }, [])

  // Load items with optimized performance and deduplication
  // FIXED: Use refs to avoid stale closure issues
  const loadItems = useCallback(async (options?: CrudOptions) => {
    try {
      setLoading(true)
      setError(null)

      // FIXED: Get current values to avoid stale closures
      const currentPage = page
      const currentPageSize = pageSize
      const currentSortBy = sortBy
      const currentSortOrder = sortOrder
      const currentFilters = filters
      const currentSearchQuery = searchQuery

      const requestParams = {
        page: currentPage,
        pageSize: currentPageSize,
        sortBy: currentSortBy || undefined,
        sortOrder: currentSortOrder,
        filters: {
          ...currentFilters,
          ...(currentSearchQuery && { search: currentSearchQuery }),
        },
        ...options,
      }

      // Create a unique key for this request to prevent duplicates
      const requestKey = `crud-${JSON.stringify(requestParams)}`

      // Prevent duplicate requests
      if (currentRequestRef.current === requestKey) {
        console.log('🚫 Duplicate request prevented:', requestKey)
        setLoading(false)
        return
      }

      // Clear any existing timeout
      if (requestTimeoutRef.current) {
        clearTimeout(requestTimeoutRef.current)
      }

      currentRequestRef.current = requestKey

      console.log('🔄 useCrud loadItems request:', {
        searchQuery,
        hasSearchQuery: !!searchQuery,
        requestParams,
        requestKey
      })

      // Import deduplication utility
      const { deduplicateRequest } = await import('../utils/apiCache')

      // Disable deduplication for search requests to ensure fresh results
      const isSearchRequest = !!searchQuery && searchQuery.trim().length > 0
      const deduplicationWindow = isSearchRequest ? 50 : 1000 // Shorter windows to prevent stale data

      const response: CrudResponse<T> = await deduplicateRequest(
        requestKey,
        async () => {
          // Add timeout to prevent hanging requests
          const timeoutPromise = new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Request timeout')), 8000)
          )

          return Promise.race([
            service.getAll(requestParams),
            timeoutPromise
          ]) as Promise<CrudResponse<T>>
        },
        deduplicationWindow
      )

      console.log('🔄 useCrud loadItems response:', {
        searchQuery,
        hasData: !!response.data,
        dataLength: response.data?.length || 0,
        total: response.total,
        currentItemsLength: items.length
      })

      setItems(response.data || [])
      setTotal(response.total || 0)
      setPage(response.page || 1)
      // Don't update pageSize from response - keep the user's requested pageSize

      console.log('🔄 useCrud state updated:', {
        newItemsLength: response.data?.length || 0,
        searchActive: !!searchQuery,
        stateUpdated: true
      })

      // Clear request tracking after successful completion
      requestTimeoutRef.current = setTimeout(() => {
        currentRequestRef.current = null
      }, 100)

    } catch (err) {
      console.error('Failed to load items:', err)
      setError(err instanceof Error ? err.message : 'Failed to load items')
      setItems([])
      // Clear request tracking on error
      currentRequestRef.current = null
    } finally {
      setLoading(false)
    }
  }, [service, page, pageSize, sortBy, sortOrder, filters, searchQuery])

  // Create item with data invalidation
  const createItem = useCallback(async (data: Partial<T>): Promise<T> => {
    try {
      setCreating(true)
      setError(null)

      const newItem = await service.create<T>(data)

      // Add to local state
      setItems(prev => [newItem, ...prev])
      setTotal(prev => prev + 1)

      // FIXED: Emit data invalidation event
      if (enableInvalidation) {
        emit(`${entityType}.created` as any, newItem)
      }

      return newItem
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create item'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setCreating(false)
    }
  }, [service, enableInvalidation, entityType, emit])

  // Update item with data invalidation
  const updateItem = useCallback(async (id: string | number, data: Partial<T>): Promise<T> => {
    try {
      setUpdating(true)
      setError(null)

      const updatedItem = await service.update<T>(id, data)

      // Update local state
      setItems(prev => prev.map(item =>
        (item as any).id === id ? updatedItem : item
      ))

      if (selectedItem && (selectedItem as any).id === id) {
        setSelectedItem(updatedItem)
      }

      // FIXED: Emit data invalidation event
      if (enableInvalidation) {
        emit(`${entityType}.updated` as any, { id, data: updatedItem })
      }

      return updatedItem
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update item'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setUpdating(false)
    }
  }, [service, selectedItem, enableInvalidation, entityType, emit])

  // Delete item with data invalidation
  const deleteItem = useCallback(async (id: string | number): Promise<void> => {
    try {
      setDeleting(true)
      setError(null)

      await service.delete(id)

      // Remove from local state
      setItems(prev => prev.filter(item => (item as any).id !== id))
      setTotal(prev => prev - 1)

      if (selectedItem && (selectedItem as any).id === id) {
        setSelectedItem(null)
      }

      // FIXED: Emit data invalidation event
      if (enableInvalidation) {
        emit(`${entityType}.deleted` as any, { id })
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete item'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setDeleting(false)
    }
  }, [service, selectedItem, enableInvalidation, entityType, emit])

  // Get single item
  const getItem = useCallback(async (id: string | number): Promise<T> => {
    try {
      setError(null)
      return await service.getById<T>(id)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get item'
      setError(errorMessage)
      throw new Error(errorMessage)
    }
  }, [service])

  // Bulk operations
  const bulkCreate = useCallback(async (itemsData: Partial<T>[]): Promise<T[]> => {
    try {
      setCreating(true)
      setError(null)

      const newItems = await service.bulkCreate<T>(itemsData)

      setItems(prev => [...newItems, ...prev])
      setTotal(prev => prev + newItems.length)

      return newItems
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create items'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setCreating(false)
    }
  }, [service])

  const bulkUpdate = useCallback(async (updates: { id: string | number; data: Partial<T> }[]): Promise<T[]> => {
    try {
      setUpdating(true)
      setError(null)

      const updatedItems = await service.bulkUpdate<T>(updates)

      setItems(prev => prev.map(item => {
        const update = updatedItems.find(updated => (updated as any).id === (item as any).id)
        return update || item
      }))

      return updatedItems
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update items'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setUpdating(false)
    }
  }, [service])

  const bulkDelete = useCallback(async (ids: (string | number)[]): Promise<void> => {
    try {
      setDeleting(true)
      setError(null)

      await service.bulkDelete(ids)

      setItems(prev => prev.filter(item => !ids.includes((item as any).id)))
      setTotal(prev => prev - ids.length)

      if (selectedItem && ids.includes((selectedItem as any).id)) {
        setSelectedItem(null)
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete items'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setDeleting(false)
    }
  }, [service, selectedItem])

  // Filter and search functions
  const handleSetFilters = useCallback((newFilters: CrudFilters) => {
    setFilters(newFilters)
    setPage(1) // Reset to first page when filters change
  }, [])

  const handleSetSearchQuery = useCallback((query: string) => {
    setSearchQuery(query)
    setPage(1) // Reset to first page when search changes

    // Clear any existing timeout to debounce search
    if (requestTimeoutRef.current) {
      clearTimeout(requestTimeoutRef.current)
    }

    // Debounce search requests
    requestTimeoutRef.current = setTimeout(() => {
      console.log('🔍 Triggering search for:', query)
      loadItems()
    }, 300) // 300ms debounce
  }, [loadItems])

  const setSorting = useCallback((newSortBy: string, newSortOrder: 'asc' | 'desc') => {
    setSortBy(newSortBy)
    setSortOrder(newSortOrder)
    setPage(1) // Reset to first page when sorting changes
  }, [])

  const clearFilters = useCallback(() => {
    setFilters(initialFilters)
    setSearchQuery('')
    setSortBy('')
    setSortOrder('asc')
    setPage(1)
  }, [initialFilters])

  // Selection
  const selectItem = useCallback((item: T | null) => {
    setSelectedItem(item)
  }, [])

  // Pagination
  const handleSetPage = useCallback((newPage: number) => {
    setPage(newPage)
  }, [])

  const handleSetPageSize = useCallback((newPageSize: number) => {
    setPageSize(newPageSize)
    setPage(1) // Reset to first page when page size changes
  }, [])

  // Export/Import
  const exportData = useCallback(async (format: 'csv' | 'excel' | 'pdf' = 'csv'): Promise<Blob> => {
    try {
      setError(null)

      // Check if backend export endpoint exists for this service
      const backendExportEndpoints = [
        'employees',
        'departments',
        'attendance',
        'projects',
        'tasks',
        'quotations',
        'workflows',
        'quality-records',
        'job-postings',
        'sales-customers',
        'sales-orders',
        'certifications',
        'training-programs',
        'vendors',
        'kpis'
      ]

      const endpoint = service.endpoint.replace(/^\/api\//, '').replace(/\/$/, '')

      if (backendExportEndpoints.includes(endpoint)) {
        // Use backend export endpoint
        const exportUrl = `/api/export/${endpoint}/?format=${format}`
        const response = await fetch(exportUrl, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        })

        if (response.ok) {
          const blob = await response.blob()
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = `${endpoint}_${new Date().toISOString().split('T')[0]}.${format}`
          document.body.appendChild(a)
          a.click()
          window.URL.revokeObjectURL(url)
          document.body.removeChild(a)
          return blob
        } else {
          throw new Error('Export failed')
        }
      } else {
        // Use client-side export for endpoints without backend support
        const { ExportService } = await import('../services/export')
        const exportService = new ExportService()

        await exportService.exportData(items, {
          format,
          filename: `${endpoint}_${new Date().toISOString().split('T')[0]}.${format}`,
          language: 'en'
        })

        // Return a dummy blob for consistency
        return new Blob([''], { type: 'text/plain' })
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to export data'
      setError(errorMessage)
      throw new Error(errorMessage)
    }
  }, [service, filters, searchQuery, items])

  const importData = useCallback(async (file: File): Promise<{ success: number; errors: string[] }> => {
    try {
      setError(null)
      const result = await service.import(file)

      // Refresh data after import
      await loadItems()

      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to import data'
      setError(errorMessage)
      throw new Error(errorMessage)
    }
  }, [service, loadItems])

  // Refresh
  const refresh = useCallback(async () => {
    await loadItems()
  }, [loadItems])

  // FIXED: Auto-load on mount and when dependencies change
  // Removed loadItems from dependency array to prevent infinite re-renders
  useEffect(() => {
    if (autoLoad) {
      loadItems()
    }
  }, [autoLoad]) // Only depend on autoLoad to prevent infinite loops

  // Search is now handled in handleSetSearchQuery with debouncing

  return {
    // Data state
    items,
    selectedItem,
    total,
    page,
    pageSize,

    // Loading states
    loading,
    creating,
    updating,
    deleting,

    // Error state
    error,

    // Filters and search
    filters,
    searchQuery,
    sortBy,
    sortOrder,

    // CRUD operations
    loadItems,
    createItem,
    updateItem,
    deleteItem,
    getItem,

    // Bulk operations
    bulkCreate,
    bulkUpdate,
    bulkDelete,

    // Search and filter
    setFilters: handleSetFilters,
    setSearchQuery: handleSetSearchQuery,
    setSorting,
    clearFilters,

    // Selection
    selectItem,

    // Pagination
    setPage: handleSetPage,
    setPageSize: handleSetPageSize,

    // Export/Import
    exportData,
    importData,

    // Refresh
    refresh,

    // Clear error
    clearError,
  }
}
